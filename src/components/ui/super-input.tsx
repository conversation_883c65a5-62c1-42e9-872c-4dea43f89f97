'use client';

import React, { useState } from 'react';
import type {
  Control,
  FieldValues,
  Path,
  RegisterOptions,
} from 'react-hook-form';
import { useController, useFormContext } from 'react-hook-form';
import { tv } from 'tailwind-variants';
import { FiEye, FiEyeOff } from 'react-icons/fi';

const inputTv = tv({
  slots: {
    container: 'relative mb-2',
    label: 'block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1',
    input:
      'w-full rounded-md border border-gray-300 dark:border-gray-700 p-3 pl-12 text-base text-gray-900 dark:text-white',
    iconContainer: 'absolute left-3 top-3.5 text-gray-500 dark:text-gray-400',
  },
  variants: {
    error: {
      true: {
        input: 'border-red-500 dark:border-red-600',
        label: 'text-red-500 dark:text-red-600',
      },
    },
    disabled: {
      true: {
        input: 'bg-gray-100 dark:bg-gray-700 cursor-not-allowed',
      },
    },
  },
  defaultVariants: {
    error: false,
    disabled: false,
  },
});

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  isPassword?: boolean;
  icon?: React.ReactNode;
  hideErrorMessage?: boolean;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ label, error, isPassword, icon, hideErrorMessage, ...props }, ref) => {
    const [showPassword, setShowPassword] = useState(!isPassword);
    const styles = inputTv({ error: !!error, disabled: props.disabled });

    return (
      <div className={styles.container()}>
        {label && <label className={styles.label()}>{label}</label>}
        <div className='relative'>
          {icon && <div className={styles.iconContainer()}>{icon}</div>}
          <input
            {...props}
            ref={ref}
            type={isPassword && !showPassword ? 'password' : 'text'}
            className={styles.input()}
          />
          {isPassword && (
            <button
              type='button'
              onClick={() => setShowPassword(!showPassword)}
              className='absolute right-3 top-3.5 text-gray-500 dark:text-gray-400'
            >
              {showPassword ? <FiEye /> : <FiEyeOff />}
            </button>
          )}
        </div>
        {error && !hideErrorMessage && (
          <p className='text-red-500 dark:text-red-600 text-sm mt-1'>{error}</p>
        )}
      </div>
    );
  }
);

export const ControlledInput = <T extends FieldValues>(props: {
  name: Path<T>;
  control?: Control<T>;
  rules?: RegisterOptions<T, Path<T>>;
  label?: string;
  isPassword?: boolean;
  icon?: React.ReactNode;
  hideErrorMessage?: boolean;
} & React.InputHTMLAttributes<HTMLInputElement>) => {
  const { control: contextControl } = useFormContext<T>();
  const { name, control = contextControl, rules, onChange: propsOnChange, ...rest } = props;

  const { field, fieldState } = useController({ name, control, rules });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    field.onChange(e);         
    if (propsOnChange) propsOnChange(e);
  };

  return (
    <Input
      {...rest}
      ref={field.ref}
      value={field.value ?? ''}
      onChange={handleChange}
      onBlur={field.onBlur}
      error={props.hideErrorMessage ? undefined : fieldState.error?.message}
    />
  );
};

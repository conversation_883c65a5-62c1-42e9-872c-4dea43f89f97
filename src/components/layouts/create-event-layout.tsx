'use client';

import { useRouter } from 'next/navigation';
import React from 'react';

type CreateEventLayoutProps = {
  title?: string;
  subTitle?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  onBackPress?: () => void;
};

export const CreateEventLayout: React.FC<CreateEventLayoutProps> = ({
  title,
  subTitle,
  children,
  footer,
  onBackPress,
}) => {
  const router = useRouter();

  const handleBack = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  return (
    <div className="flex flex-col min-h-screen px-safe pt-safe pb-safe">
      <header className="flex items-center justify-between px-4 py-3 border-b border-gray-200 dark:border-gray-700">
        <button
          onClick={handleBack}
          aria-label="Go back"
          className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
        >
          ← Back
        </button>

        <button
          onClick={() => router.push('/')}
          aria-label="Go home"
          className="text-purple-600 hover:text-purple-800"
        >
          
          Home
        </button>
      </header>

      <main className="flex flex-col flex-1 mx-4 mt-4 gap-1">
        {title && <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">{title}</h1>}
        {subTitle && (
          <p className="text-gray-600 dark:text-gray-400">{subTitle}</p>
        )}
      </main>

      <section className="flex flex-col flex-1 gap-4 p-4">{children}</section>

      {footer && <footer className="mt-auto px-4 pb-4">{footer}</footer>}
    </div>
  );
};
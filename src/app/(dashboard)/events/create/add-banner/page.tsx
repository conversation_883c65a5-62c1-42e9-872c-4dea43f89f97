import React, { useState, useCallback, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import ReactCrop, { Crop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { useDropzone } from 'react-dropzone';

import { Button } from '@/components/ui';
import type { CreateEventFormType } from '@/lib/hooks';

const MAX_MEDIA_COUNT = 2;


  type BannerData = {
    uri: string;
    type?: string;
    name?: string | null;
    size?: number;
  };

  const fileToBanner = (file: File): BannerData => ({
    uri: URL.createObjectURL(file),
    name: file.name,
    size: file.size,
    type: file.type,
  });

function getFileNameFromPath(path: string) {
  return path.split(/[/\\]/).pop() ?? 'file.jpg';
}

function getCroppedImg(
  image: HTMLImageElement,
  crop: Crop
): Promise<Blob | null> {
  const canvas = document.createElement('canvas');
  const scaleX = image.naturalWidth / image.width;
  const scaleY = image.naturalHeight / image.height;
  canvas.width = crop.width!;
  canvas.height = crop.height!;
  const ctx = canvas.getContext('2d');

  if (!ctx) return Promise.resolve(null);

  ctx.drawImage(
    image,
    crop.x! * scaleX,
    crop.y! * scaleY,
    crop.width! * scaleX,
    crop.height! * scaleY,
    0,
    0,
    crop.width!,
    crop.height!
  );

  return new Promise((resolve) => {
    canvas.toBlob(
      (blob) => {
        resolve(blob);
      },
      'image/jpeg',
      0.8
    );
  });
}

export default function AddBanner() {
  const { watch, setValue } = useFormContext<CreateEventFormType>();
  const router = useRouter();

  const bannerObj = watch('bannerObj');
  const currentMedia = watch('media') || [];

  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [crop, setCrop] = useState<Crop>({
  unit: '%',
  x: 10,
  y: 10,
  width: 80,
  height: 60, // width / (4/3) to roughly respect 4:3
});
  const [croppedBlob, setCroppedBlob] = useState<Blob | null>(null);
  const [imageRef, setImageRef] = useState<HTMLImageElement | null>(null);
  const [isCroppingBanner, setIsCroppingBanner] = useState(false);
  const [mediaFiles, setMediaFiles] = useState<BannerData[]>(currentMedia as BannerData[]);

  // Update mediaFiles from form media on external changes
  useEffect(() => {
    setMediaFiles(currentMedia as BannerData[]);
  }, [currentMedia]);

  // Dropzone for media upload
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return;

      // If banner is missing, first file is banner
      if (!bannerObj?.uri) {
        setSelectedImage(acceptedFiles[0]);
        setIsCroppingBanner(true);
        if (acceptedFiles.length > 1) {
          // Add remaining files to media
          const moreFiles = acceptedFiles
            .slice(1, MAX_MEDIA_COUNT + 1)
            .map(fileToBanner);
          const updatedMedia = [...mediaFiles, ...moreFiles].slice(
            0,
            MAX_MEDIA_COUNT
          );
          setMediaFiles(updatedMedia);
          setValue('media', updatedMedia as any);
        }
      } else {
        // Add all files as media up to max count
        const newItems = acceptedFiles.map(fileToBanner);
        const updatedMedia = [...mediaFiles, ...newItems].slice(
          0,
          MAX_MEDIA_COUNT
        );
        setMediaFiles(updatedMedia);
        setValue('media', updatedMedia as any);
      }
    },
    [bannerObj, mediaFiles, setValue]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'image/*': [],
    },
    onDrop,
    maxFiles: MAX_MEDIA_COUNT + 1,
  });

  const onImageLoaded = (img: HTMLImageElement) => {
    setImageRef(img);
  };

  const onCropComplete = async () => {
    if (imageRef && crop.width && crop.height) {
      const blob = await getCroppedImg(imageRef, crop);
      setCroppedBlob(blob);
    }
  };

  const saveBanner = () => {
    if (croppedBlob) {
      const file = new File(
        [croppedBlob],
        selectedImage?.name || 'banner.jpg',
        { type: 'image/jpeg' }
      );
      const bannerData = {
        uri: URL.createObjectURL(file),
        name: file.name,
        size: file.size,
        type: file.type,
      };
      setValue('bannerObj', bannerData);
      setIsCroppingBanner(false);
      setSelectedImage(null);
    }
  };

  const removeMediaAt = (index: number) => {
    const newMedia = [...mediaFiles];
    newMedia.splice(index, 1);
    setMediaFiles(newMedia);
    setValue('media', newMedia);
  };

  const continueDisabled = !bannerObj?.uri;

  return (
    <div className='p-4 max-w-xl mx-auto'>
      <h1 className='text-xl font-bold mb-4'>Add event image</h1>
      <p className='mb-6 text-gray-600'>Add an event image</p>

      {isCroppingBanner && selectedImage && (
        <div>
          <ReactCrop
            crop={crop}
            onChange={(c) => setCrop(c)}
            onComplete={onCropComplete}
            circularCrop={false}
            aspect={4 / 3}
          >
            <img
              src={URL.createObjectURL(selectedImage)}
              onLoad={(e) => onImageLoaded(e.currentTarget)}
            />
          </ReactCrop>
          <Button label='Save Banner' onClick={saveBanner} />
          <Button label='Cancel' onClick={() => setIsCroppingBanner(false)} />
        </div>
      )}

      {!isCroppingBanner && (
        <>
          {bannerObj?.uri ? (
            <div className='relative mb-4'>
              <img
                src={bannerObj.uri}
                alt='Banner'
                className='w-full rounded-lg object-cover'
                style={{ aspectRatio: '4 / 3' }}
              />
              <Button onClick={() => setIsCroppingBanner(true)}>
                Change Banner
              </Button>
            </div>
          ) : (
            <div
              {...getRootProps()}
              className={`border border-dashed p-6 text-center cursor-pointer ${
                isDragActive ? 'border-blue-500 bg-blue-50' : ''
              }`}
            >
              <input {...getInputProps()} />
              <p>
                Drag 'n' drop an event banner here, or click to select files
              </p>
              <p className='text-sm text-gray-500'>
                You can also upload additional media after banner
              </p>
            </div>
          )}

          <div className='flex flex-wrap gap-2 mt-4'>
            {mediaFiles.map((file, index) => (
              <div
                key={file.name || file.uri}
                className='relative w-24 h-24 rounded overflow-hidden border'
              >
                <img
                  src={file.uri}
                  alt={`Media ${index + 1}`}
                  className='w-full h-full object-cover'
                />
                <button
                  type='button'
                  onClick={() => removeMediaAt(index)}
                  className='absolute top-0 right-0 bg-red-600 text-white rounded-full w-5 h-5 flex items-center justify-center'
                >
                  ×
                </button>
              </div>
            ))}
            {mediaFiles.length < MAX_MEDIA_COUNT && bannerObj?.uri && (
              <div
                {...getRootProps()}
                className='w-24 h-24 flex flex-col items-center justify-center border border-dashed rounded cursor-pointer'
              >
                <input {...getInputProps()} />
                <span className='text-3xl'>+</span>
                <span className='text-xs text-gray-500'>
                  Add {MAX_MEDIA_COUNT - mediaFiles.length} more photo
                  {MAX_MEDIA_COUNT - mediaFiles.length !== 1 ? 's' : ''}
                </span>
              </div>
            )}
          </div>
        </>
      )}

      <Button
        label='Continue'
        className='mt-6'
        disabled={continueDisabled}
        onClick={() => router.push('/events/create/choose-format')}
      />
    </div>
  );
}

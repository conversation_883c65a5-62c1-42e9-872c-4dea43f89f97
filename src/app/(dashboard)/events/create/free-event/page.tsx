import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useFormContext, useFieldArray } from 'react-hook-form';

import { CreateEventLayout } from '@/components/layouts';
import { FeatureToggle, Input } from '@/components/ui';
import {
  Button,
  colors,
  Modal,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui';
import { type CreateEventFormType } from '@/lib';
import { Close } from '@/components/ui/close';
import { ChevronDown } from 'lucide-react';
import { IoClose } from 'react-icons/io5';

export const FIELD_TYPES = [
  { label: 'Alpha-numeric', value: 'alpha-numeric' },
  { label: 'Numeric', value: 'numeric' },
  { label: 'Age range selector', value: 'age-range-selector' },
  { label: 'Country selector', value: 'country-selector' },
  { label: 'Sex selector', value: 'sex-selector' },
];

export default function FreeEventDetails() {
  const { watch, control, setValue } = useFormContext<CreateEventFormType>();
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'registrationFields',
  });

  const [fieldName, setFieldName] = useState('');
  const [fieldType, setFieldType] =
    useState<(typeof FIELD_TYPES)[number]['value']>('alpha-numeric');

  const router = useRouter();
  const [openFieldsModal, setOpenFieldsModal] = useState(false);

  const alert = (message: string) => window.alert(message);

  const handleAddField = () => {
  if (!fieldName || !fieldType) return;
  const isDuplicate = fields.some(
    (field) => field.name.trim().toLowerCase() === fieldName.trim().toLowerCase()
  );
  if (isDuplicate) {
    alert('Field already exists. Please use a unique field name.');
    return;
  }
  append({ name: fieldName.trim(), type: fieldType });
  setFieldName('');
  setFieldType('alpha-numeric');
  setOpenFieldsModal(false);
};

  const handleToggle = (checked: boolean) => {
    setValue('isRegistrationRequired', checked);
  };

  const StaticField = ({ label }: { label: string }) => (
  <div className='min-h-12 flex-row items-start justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark'>
    <p className='text-neutral-400 dark:text-neutral-500'>{label}</p>
    <p className='text-neutral-400 dark:text-neutral-500'>(Required)</p>
  </div>
);

  return (
    <CreateEventLayout
      title='Set up free event'
      subTitle='Choose between requiring attendees to register for the event or just uploading your event.'
      footer={
        <Button
          data-testid='account-selection-button'
          label='Continue'
          className='mx-4'
          onClick={() => router.push('/events/create/free-event-next')}
        />
      }
    >
      <div className='gap-4'>
        <FeatureToggle
          onChange={(e) => handleToggle(e)}
          aria-label='Enable registration'
          checked={!!watch('isRegistrationRequired')}
          title='Enable registration'
          accessibilityLabel='Enable registration'
        />

        {watch('isRegistrationRequired') && (
          <>
            <StaticField label="Full name" />
            <StaticField label="Email" />

            {fields.map((field, index) => (
              <div
                key={field.id}
                className='relative min-h-12 flex-row items-start justify-between rounded-md border border-border-subtle-light p-3 dark:border-border-subtle-dark'
              >
                <p className='text-neutral-400 dark:text-neutral-500'>
                  {field.name}
                </p>
                {(field.type === 'age-range-selector' ||
                  field.type === 'country-selector' ||
                  field.type === 'sex-selector') && (
                  <div>
                    <ChevronDown
                      size={20}
                      color={colors.grey[70]}
                    />
                  </div>
                )}
                <div className='absolute -right-1 -top-1 z-10'>
                  <Close onClick={() => remove(index)} />
                </div>
              </div>
            ))}

            <Button
              label='Add field +'
              variant='secondary'
              className='w-[154px] py-2'
              size='sm'
              disabled={!watch('isRegistrationRequired')}
              onClick={() => setOpenFieldsModal(true)}
            />
          </>
        )}
      </div>

      <Modal isOpen={openFieldsModal} onClose={() => setOpenFieldsModal(false)}>
        <div className='flex-1 gap-4 px-4 pb-4'>
          <div className='mb-4 flex-row items-center justify-start gap-2'>
          <button
            onClick={() => setOpenFieldsModal(false)}
            aria-label='Close category modal'
            className="p-1"
          >
            <IoClose size={32} color={colors.grey[70]} />
          </button>
            <p className='text-lg font-bold dark:text-neutral-100'>
              Add field
            </p>
          </div>
          <Input
            label='Field name'
            value={fieldName}
            onChange={(e) => setFieldName(e.target.value)}
          />
          <Select
  value={fieldType}
  onValueChange={(val) => setFieldType(val as typeof fieldType)}
>
  <SelectTrigger>
    <SelectValue placeholder="Choose field type" />
  </SelectTrigger>

  <SelectContent>
    {FIELD_TYPES.map((opt) => (
      <SelectItem key={opt.value} value={opt.value}>
        {opt.label}
      </SelectItem>
    ))}
  </SelectContent>
</Select>

          <div className='mt-auto pb-4'>
            <Button
              label='Save'
              onClick={handleAddField}
              disabled={!fieldName || !fieldType}
            />
          </div>
        </div>
      </Modal>
    </CreateEventLayout>
  );
}

'use client';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { useRouter } from 'next/navigation';

import { GradientBorderCard } from '@/components/cards/gradient-border-card';
import { CreateEventLayout } from '@/components/layouts';
import { Button, H2, Image, Modal, P } from '@/components/ui';
import { type CreateEventFormType } from '@/lib/hooks';
import { EVENT_TYPE_CARDS } from '@/lib/constants';

export default function ChooseEventVisibility() {
  const { watch, setValue } = useFormContext<CreateEventFormType>();
  const router = useRouter();

  const [isModalOpen, setIsModalOpen] = React.useState(false);

  const eventType = watch('eventType');

  const handleContinue = () => {
    if (eventType === 'PRIVATE') {
      setIsModalOpen(true);
    } else {
      router.push('/events/create/add-details');
    }
  };

  const handleMakePrivate = () => {
    setIsModalOpen(false);
    router.push('/events/create/add-details');
  };

  return (
    <CreateEventLayout
      title='What type of event are you hosting?'
      subTitle='Choose whether your event is public or private.'
      footer={
        <Button
          label='Continue'
          className='m-4'
          disabled={!eventType}
          onClick={handleContinue}
        />
      }
    >
      {EVENT_TYPE_CARDS.map((card) => (
        <GradientBorderCard
          key={card.id}
          isSelected={eventType === card.id}
          onClick={() => setValue('eventType', card.id)}
          icon={card.icon}
          title={card.title}
          description={card.description}
        />
      ))}

      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <div className='gap-6 px-4 pb-8'>
          <Image
            src='/icons/events/confirm-private.png'
            alt='Confirm private event'
            className='w-[120px] mx-auto'
          />

          <div className='gap-4'>
            <H2 className='text-center'>Make this event private?</H2>
            <P className='text-center text-fg-muted-light dark:text-fg-muted-dark'>
              By making this event private, you will not be able to list it
              publicly. Attendees will require an access code to be part of this
              event.
            </P>
          </div>

          <div className='flex flex-row justify-between gap-2 py-4'>
            <Button
              label='Cancel'
              variant='secondary'
              onClick={() => setIsModalOpen(false)}
              className='flex-1'
            />
            <Button
              label='Make private'
              onClick={handleMakePrivate}
              className='flex-1'
            />
          </div>
        </div>
      </Modal>
    </CreateEventLayout>
  );
}
